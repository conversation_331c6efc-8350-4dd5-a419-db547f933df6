import SwiftUI
import Foundation

let planFocusWeekDefaultsKey = "plans.focusWeekStart"

// Types moved to Models/ and Services/ to avoid duplication.
// MVP-5 timeout utilities for generation timeout protection
struct TimeoutError: Error {}

func withTimeout<T: Sendable>(
    _ seconds: TimeInterval,
    operation: @escaping @Sendable () async throws -> T
) async throws -> T {
    let clampedSeconds = min(max(seconds, 0.001), 600.0)
    let nanoseconds = UInt64(clampedSeconds * 1_000_000_000)
    return try await withThrowingTaskGroup(of: T.self) { group in
        group.addTask { try await operation() }
        group.addTask {
            try await Task.sleep(nanoseconds: nanoseconds)
            throw TimeoutError()
        }
        let result = try await group.next()!
        group.cancelAll()
        return result
    }
}


struct PlanSaveBanner: Equatable {
    let message: String
    let focusWeekStart: Date
}


@Observable
@MainActor
class RecipeGeneratorViewModel {
    private let recipeService: RecipeGenerationServiceProtocol
    private let authService: AuthenticationService
    var navigationCoordinator: NavigationCoordinator?

    // P0 Core State (aligned with PRD 2.1)
    var mode: UIMode = .quick
    var customConfiguration: CustomConfiguration = .init()
    var quickConfiguration: QuickConfiguration = .init()
    var userSelectedStartDate: Date = RecipeGeneratorViewModel.clampStartDate(Date()) {
        didSet {
            let clamped = RecipeGeneratorViewModel.clampStartDate(userSelectedStartDate)
            if clamped != userSelectedStartDate {
                userSelectedStartDate = clamped
            }
        }
    }
    var viewState: ViewState = .idle
    var pantryState: PantryState = .loading
    // Toast state for Quick generation preview (V6 Task 1.1)
    var toastState: GeneratorToastState = .none
    // Notice for capacity/full situations (Quick history)
    var capacityAlert: String? = nil
    // Confirmation toast message after saving to Quick (C3.3)
    var saveToastMessage: String? = nil

    var planSaveBanner: PlanSaveBanner?

    private var bannerDismissTask: Task<Void, Never>?

    // Generation task handle (P0 2.3)
    private var currentGenerationTask: Task<Void, Never>?

    // Cache custom config when switching modes (P0 2.4)
    private var cachedCustomConfiguration: CustomConfiguration?
    private var cachedCustomStartDate: Date?

    var canGenerate: Bool {
        // Pantry must have items
        guard case .hasItems = pantryState else { return false }
        // Not while loading
        if case .loading = viewState { return false }
        switch mode {
        case .quick:
            // Quick: minimal inputs; defaults are valid
            return true
        case .custom:
            // V4: Relax enablement — only core required fields
            let maxDays = RemoteConfigurationManager.shared.configuration.maxDays
            guard !customConfiguration.selectedMeals.isEmpty else { return false }
            guard (1...maxDays).contains(customConfiguration.days) else { return false }
            // Do not require per-meal configs here; we will sanitize before building the request
            return true
        }
    }

    // P1 2.5: Configuration summary for Custom mode (Phase 1 trimmed)
    var configurationSummary: String {
        guard mode == .custom else { return "" }
        if customConfiguration.selectedMeals.isEmpty {
            return NSLocalizedString("config_summary_empty", comment: "Please select at least one meal")
        }

        let mealsText = customConfiguration.selectedMeals.map { $0.displayName }.joined(separator: ", ")
        let daysText = customConfiguration.days == 1
            ? NSLocalizedString("config_summary_one_day", comment: "1 day")
            : String(format: NSLocalizedString("config_summary_days", comment: "%d days"), customConfiguration.days)

        let summary = String(format: NSLocalizedString("config_summary_planning", comment: "Planning %@ of %@"), daysText, mealsText)
        return summary
    }

    // Pantry state provider injection
    private let pantryStateProvider: PantryStateProvider = DefaultPantryStateProvider(pantryService: ServiceContainer.shared.pantryService)

    init(
        recipeService: RecipeGenerationServiceProtocol = ServiceContainer.shared.recipeGenerationService,
        authService: AuthenticationService = ServiceContainer.shared.authenticationService
    ) {
        self.recipeService = recipeService
        self.authService = authService

        // Observe pantry state changes
        Task { [weak self] in
            guard let self = self else { return }
            for await state in pantryStateProvider.observePantryChanges() {
                self.pantryState = state
            }
        }

        // Initialize pantry state
        Task { [weak self] in
            guard let self = self else { return }
            self.pantryState = await pantryStateProvider.checkPantryState()
        }
    }

    deinit {
        bannerDismissTask?.cancel()
    }

    /// Get user's dietary restrictions as strings (fixed allergies/intolerances accessor per Phase 1)
    var userDietaryRestrictions: [String] {
        if let preferences = authService.userPreferences {
            let restrictions = preferences.dietaryRestrictions.map { $0.rawValue }
            let allergiesIntolerances = preferences.allergiesIntolerances.map { $0.rawValue }
            let strictExclusions = preferences.strictExclusions.map { $0.rawValue }

            let allRestrictions = restrictions + allergiesIntolerances + strictExclusions
            return Array(Set(allRestrictions)) // Remove duplicates
        }
        return []
    }

    /// Generate user preferences context string for recipe generation
    var userPreferencesContext: String {
        guard let preferences = authService.userPreferences else { return "" }

        var context = ""

        // Family size
        context += "- Family size: \(preferences.familySize) people\n"

        // Dietary restrictions
        if !preferences.dietaryRestrictions.isEmpty {
            context += "- Dietary preferences: \(preferences.dietaryRestrictions.map { $0.rawValue }.joined(separator: ", "))\n"
        }

        // Allergies and Intolerances (fixed accessor per Phase 1)
        if !preferences.allergiesIntolerances.isEmpty {
            context += "- Allergies/Intolerances: \(preferences.allergiesIntolerances.map { $0.rawValue }.joined(separator: ", "))\n"
        }

        // Strict exclusions
        if !preferences.strictExclusions.isEmpty {
            context += "- Must exclude: \(preferences.strictExclusions.map { $0.rawValue }.joined(separator: ", "))\n"
        }

        // Kitchen equipment (Phase 2)
        if !preferences.equipmentOwned.isEmpty {
            context += "- Available equipment: \(preferences.equipmentOwned.joined(separator: ", "))\n"
        }

        return context
    }


    /// Public API used by UI (kept for compatibility). Starts a cancellable task and awaits its completion.
    func generateRecipeIdeas(cookingTimeMinutes: Int) async {
        await generateRecipes(cookingTimeMinutes: cookingTimeMinutes)
    }

    /// New P0 API: create task handle, animate to loading, and perform generation.
    func generateRecipes(cookingTimeMinutes: Int) async {
        // Cancel any in-flight task
        currentGenerationTask?.cancel()
        dismissPlanSaveBanner()
        withAnimation { viewState = .loading }
        let task = Task { [weak self] in
            guard let self else { return }
            await self.performGeneration(cookingTimeMinutes: cookingTimeMinutes)
        }
        currentGenerationTask = task
        // For tests/UI awaiting completion
        _ = await task.value
    }

    /// Cancel current generation task and reset state with haptic feedback.
    func cancelGeneration() {
        currentGenerationTask?.cancel()
        currentGenerationTask = nil
        withAnimation { viewState = .idle }
        Haptics.light()
    }

    private func performGeneration(cookingTimeMinutes: Int) async {
        // Early exit if cancelled
        if Task.isCancelled { return }
        // Build request with user equipment (Phase 2)
        let userEquipment = authService.userPreferences?.equipmentOwned ?? []
        let buildResult = RecipeRequestBuilder.build(mode: mode, custom: customConfiguration, quick: quickConfiguration, pantryState: pantryState, userEquipment: userEquipment)
        switch buildResult {
        case .failure(let e):
            if Task.isCancelled { return }
            switch e {
            case .pantryEmpty:
                viewState = .failed(.pantryEmpty)
            case .invalidConfiguration(let reason):
                viewState = .failed(.configurationError(reason))
            }
        case .success(let request):
            do {
                if Task.isCancelled { return }
                if mode == .quick {
                    // Quick: generate and present toast preview (save only on "Good")
                    let adapter = RecipeServiceAdapter(recipeService: recipeService, pantryService: ServiceContainer.shared.pantryService)
                    let uiModels = try await adapter.generate(using: request, cookingTimeMinutes: cookingTimeMinutes, authService: authService)
                    if Task.isCancelled { return }
                    // Keep loaded state for potential UI/tests, but do not auto-save
                    viewState = .loaded(uiModels)
                    toastState = .preview(items: uiModels)
                } else {
                    // Custom/Plans: use structured generator with date window + cutoffs
                    let mealPlanReqResult = RecipeRequestBuilder.buildMealPlanRequest(from: customConfiguration, startDate: userSelectedStartDate, userEquipment: userEquipment)
                    switch mealPlanReqResult {
                    case .failure(let err):
                        if Task.isCancelled { return }
                        viewState = .failed(.configurationError("\(err)"))
                    case .success(let planReq):
                        let currentFingerprint = GenerationFingerprint.current(
                            from: customConfiguration,
                            startDate: userSelectedStartDate,
                            equipment: userEquipment
                        )
                        let previousFingerprint = GenerationFingerprint.load()
                        // Prevent stale navigation to old results when configuration is unchanged
                        let shouldNavigateToPlans = previousFingerprint != currentFingerprint

                        let plan = try await withTimeout(45) { [self] in
                            try await self.recipeService.generateStructuredMealPlan(
                                planReq,
                                pantryService: ServiceContainer.shared.pantryService,
                                authService: self.authService
                            )
                        }
                        if Task.isCancelled { return }
                        let summary = PlanStore.shared.mergeAndSave(newPlan: plan, policy: .appendAll)
                        let banner = makePlanSaveBanner(from: summary)
                        applyPlanSaveBanner(banner)
                        let focusWeekStart = banner?.focusWeekStart ?? plan.days.first.map { WeeklyMealPlan(referenceDate: $0.date, calendar: Calendar.current).weekStart }
                        // Flatten for legacy ViewState (temporary)
                        let uiModels = plan.days.flatMap { $0.meals.map { $0.recipe } }
                        viewState = .loaded(uiModels)
                        currentFingerprint.store()
                        if shouldNavigateToPlans {
                            if let focusWeekStart {
                                storeFocusWeekStart(focusWeekStart)
                            }
                            // Hint Recipes screen to land on Plans tab on next navigation
                            UserDefaults.standard.set("plans", forKey: "recipes.selectedTab")
                            navigationCoordinator?.switchToTab(3)
                        }
                    }
                }
            } catch is TimeoutError {
                if Task.isCancelled { return }
                viewState = .failed(.serviceError(message: NSLocalizedString("error_generation_timeout", comment: "")))
            } catch {
                if Task.isCancelled { return }
                viewState = .failed(.serviceError(message: error.localizedDescription))
            }
        }
    }

    // MARK: - Plan Save Banner

    private func makePlanSaveBanner(from summary: OverlapSaveSummary) -> PlanSaveBanner? {
        guard summary.added > 0 else { return nil }
        guard
            let message = Self.bannerMessage(from: summary),
            let firstDate = summary.addedCountsByDayMeal.keys.min()
        else { return nil }
        let focusWeekStart = WeeklyMealPlan(referenceDate: firstDate, calendar: Calendar.current).weekStart
        return PlanSaveBanner(message: message, focusWeekStart: focusWeekStart)
    }

    private static func bannerMessage(from summary: OverlapSaveSummary) -> String? {
        let calendar = Calendar.current
        let formatter = DateFormatter()
        formatter.calendar = calendar
        formatter.locale = Locale.current
        formatter.dateFormat = "EEE"

        let orderedMeals = MealType.allCases
        let components: [String] = summary.addedCountsByDayMeal
            .sorted { $0.key < $1.key }
            .flatMap { date, mealCounts -> [String] in
                let day = formatter.string(from: date)
                return mealCounts
                    .filter { $0.value > 0 }
                    .sorted { lhs, rhs in
                        guard
                            let leftIndex = orderedMeals.firstIndex(of: lhs.key),
                            let rightIndex = orderedMeals.firstIndex(of: rhs.key)
                        else { return lhs.key.rawValue < rhs.key.rawValue }
                        return leftIndex < rightIndex
                    }
                    .map { meal, count in
                        "\(day) \(meal.displayName) ×\(count)"
                    }
            }

        guard components.isEmpty == false else { return nil }
        let joined = components.joined(separator: ", ")
        let format = NSLocalizedString("meal_plan_save_banner_prefix", comment: "Banner prefix showing added meal plan slots")
        return String(format: format, joined)
    }

    private func applyPlanSaveBanner(_ banner: PlanSaveBanner?) {
        bannerDismissTask?.cancel()
        bannerDismissTask = nil
        planSaveBanner = banner
        guard banner != nil else { return }

        bannerDismissTask = Task { [weak self] in
            try? await Task.sleep(nanoseconds: 4_000_000_000)
            guard !Task.isCancelled else { return }
            await MainActor.run {
                self?.planSaveBanner = nil
            }
        }
    }

    private func dismissPlanSaveBanner() {
        bannerDismissTask?.cancel()
        bannerDismissTask = nil
        planSaveBanner = nil
    }

    private func storeFocusWeekStart(_ weekStart: Date) {
        UserDefaults.standard.set(weekStart.timeIntervalSince1970, forKey: planFocusWeekDefaultsKey)
    }

    func handlePlanSaveBannerCTA() {
        guard let banner = planSaveBanner else { return }
        storeFocusWeekStart(banner.focusWeekStart)
        UserDefaults.standard.set("plans", forKey: "recipes.selectedTab")
        navigationCoordinator?.switchToTab(3)
        dismissPlanSaveBanner()
    }

    // MARK: - Mode switching (P0 2.4)

    func selectMode(_ newMode: UIMode) {
        guard newMode != mode else { return }
        // Cache current custom config when leaving custom
        if mode == .custom {
            cachedCustomConfiguration = customConfiguration
            cachedCustomStartDate = userSelectedStartDate
        }
        cancelGeneration()
        withAnimation(.easeInOut) {
            mode = newMode
            // Restore cached custom config when entering custom
            if newMode == .custom, let cached = cachedCustomConfiguration {
                customConfiguration = cached
                if let cachedDate = cachedCustomStartDate {
                    userSelectedStartDate = cachedDate
                }
            }
            viewState = .idle
        }
    }



    /// Clear results (reset state)
    func clearResults() {
        viewState = .idle
    }

    // MARK: - Toast Actions (Quick)

    /// Accept the toast preview: save and navigate to Recipes > Quick
    func acceptQuickPreview() {
        guard case .preview(let items) = toastState else { return }
        // Respect Quick capacity (10). Do not auto-evict when full.
        if !QuickHistoryManager.shared.canAcceptNew {
            // Show non-blocking notice and keep user on Generator
            capacityAlert = "Quick Recipes full (10). Delete one to save new results."
            toastState = .none
            return
        }
        let entry = QuickResultHistory(
            mealType: quickConfiguration.mealType,
            numberOfDishes: quickConfiguration.numberOfDishes,
            totalCookTime: quickConfiguration.totalTimeMinutes,
            cuisines: quickConfiguration.cuisines,
            additionalRequest: quickConfiguration.additionalRequest.isEmpty ? nil : quickConfiguration.additionalRequest,
            recipes: items
        )
        _ = QuickHistoryManager.shared.save(entry)
        // Prepare confirmation toast message clarifying generation vs dishes (C3.3)
        let dishes = items.count
        self.saveToastMessage = "Saved \(dishes) dish\(dishes == 1 ? "" : "es") (1 generation) to Quick."
        toastState = .none
        // Target Recipes > Quick sub-tab before switching tabs (B2.1)
        UserDefaults.standard.set("quick", forKey: "recipes.selectedTab")
        // Notify QuickHistoryView to show a temporary "New" badge on the latest entry (B2.2)
        NotificationCenter.default.post(name: .quickHistoryNewEntryAdded, object: nil, userInfo: ["id": entry.id])
        navigationCoordinator?.switchToTab(3)
    }

    /// Dismiss the toast and allow user to adjust inputs and regenerate
    func dismissQuickPreview() {
        toastState = .none
    }

    /// Format cooking time for display
    func formatCookingTime(_ minutes: Int) -> String {
        if minutes < 60 {
            return "\(minutes) min"
        } else {
            let hours = minutes / 60
            let remainingMinutes = minutes % 60
            if remainingMinutes == 0 {
                return "\(hours) hr"
            } else {
                return "\(hours) hr \(remainingMinutes) min"
            }
        }
    }

    /// Check if recipe matches user preferences
    func isRecipeCompatible(with recipe: Recipe) -> Bool {
        guard authService.userPreferences != nil else { return true }

        // Check against strict exclusions and allergies
        let userRestrictions = Set(userDietaryRestrictions.map { $0.lowercased() })
        let recipeIngredients = Set(recipe.ingredients.map { $0.lowercased() })

        // If any user restriction appears in recipe ingredients, it's not compatible
        return userRestrictions.isDisjoint(with: recipeIngredients)
    }

    private static func clampStartDate(_ date: Date) -> Date {
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())
        guard let maxDate = calendar.date(byAdding: .day, value: 7, to: today) else {
            return today
        }
        let start = calendar.startOfDay(for: date)
        if start < today { return today }
        if start > maxDate { return maxDate }
        return start
    }
}
